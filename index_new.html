<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>实时文件监控与显示</title>
    <style>
        body { 
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; 
            background-color: #f0f2f5; 
            color: #333; 
            margin: 0;
            padding: 20px;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            padding: 20px; 
            background-color: #fff; 
            box-shadow: 0 0 10px rgba(0,0,0,0.1); 
            border-radius: 8px; 
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .status { 
            font-weight: bold; 
            padding: 8px 12px; 
            border-radius: 4px; 
            text-align: center; 
        }
        .connected { 
            color: #28a745; 
            background-color: #e9f7eb; 
        }
        .disconnected { 
            color: #dc3545; 
            background-color: #fce8e6; 
        }
        .content-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            min-height: 300px;
            max-height: 70vh;
            overflow: auto;
        }
        .file-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .file-name {
            font-weight: bold;
            color: #0066cc;
        }
        .timestamp {
            color: #666;
            font-size: 12px;
        }
        .content-display {
            margin-top: 10px;
        }
        .text-content {
            white-space: pre-wrap;
            word-wrap: break-word;
            background-color: #272822;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', Courier, monospace;
        }
        .image-content {
            max-width: 100%;
            max-height: 500px;
            display: block;
            margin: 0 auto;
            border: 1px solid #ddd;
        }
        .json-content {
            white-space: pre-wrap;
            word-wrap: break-word;
            background-color: #272822;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', Courier, monospace;
        }
        .binary-content {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            color: #666;
            font-style: italic;
        }
        .history-container {
            margin-top: 20px;
        }
        .history-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #555;
        }
        .history-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 0;
            margin: 0;
            list-style-type: none;
        }
        .history-item {
            padding: 8px 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .history-item:hover {
            background-color: #f5f5f5;
        }
        .history-item:last-child {
            border-bottom: none;
        }
        .error-message {
            color: #dc3545;
            background-color: #fce8e6;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>实时文件监控与显示</h1>
            <div id="status" class="status">正在连接...</div>
        </div>
        
        <div class="content-container">
            <div class="file-info">
                <div>文件名: <span id="file-name" class="file-name">等待文件更新...</span></div>
                <div>路径: <span id="file-path">-</span></div>
                <div>类型: <span id="content-type">-</span></div>
                <div>时间: <span id="timestamp" class="timestamp">-</span></div>
            </div>
            
            <div id="content-display" class="content-display">
                <div class="binary-content">等待服务器推送文件内容...</div>
            </div>
        </div>
        
        <div class="history-container">
            <div class="history-title">历史记录 (最近10条)</div>
            <ul id="history-list" class="history-list"></ul>
        </div>
    </div>

    <script>
        // DOM 元素
        const statusDiv = document.getElementById('status');
        const fileNameSpan = document.getElementById('file-name');
        const filePathSpan = document.getElementById('file-path');
        const contentTypeSpan = document.getElementById('content-type');
        const timestampSpan = document.getElementById('timestamp');
        const contentDisplayDiv = document.getElementById('content-display');
        const historyListUl = document.getElementById('history-list');
        
        // 历史记录
        const fileHistory = [];
        const MAX_HISTORY = 10;
        
        // WebSocket 连接
        const socket = new WebSocket(`ws://localhost:8765`);
        
        // 格式化时间戳
        function formatTimestamp(timestamp) {
            const date = new Date(timestamp * 1000);
            return date.toLocaleString();
        }
        
        // 添加到历史记录
        function addToHistory(fileData) {
            // 如果已经存在相同文件名的记录，先移除它
            const existingIndex = fileHistory.findIndex(item => item.filename === fileData.filename);
            if (existingIndex !== -1) {
                fileHistory.splice(existingIndex, 1);
            }
            
            // 添加到历史记录开头
            fileHistory.unshift(fileData);
            
            // 保持历史记录不超过最大数量
            if (fileHistory.length > MAX_HISTORY) {
                fileHistory.pop();
            }
            
            // 更新历史记录UI
            updateHistoryUI();
        }
        
        // 更新历史记录UI
        function updateHistoryUI() {
            historyListUl.innerHTML = '';
            
            fileHistory.forEach((item, index) => {
                const li = document.createElement('li');
                li.className = 'history-item';
                li.textContent = `${item.filename} (${formatTimestamp(item.timestamp)})`;
                li.addEventListener('click', () => displayFileContent(item));
                historyListUl.appendChild(li);
            });
        }
        
        // 显示文件内容
        function displayFileContent(fileData) {
            // 更新文件信息
            fileNameSpan.textContent = fileData.filename || '未知文件';
            filePathSpan.textContent = fileData.path || '-';
            contentTypeSpan.textContent = fileData.content_type || '未知类型';
            timestampSpan.textContent = fileData.timestamp ? formatTimestamp(fileData.timestamp) : '-';
            
            // 清空内容显示区域
            contentDisplayDiv.innerHTML = '';
            
            // 根据内容类型显示内容
            if (fileData.content_base64) {
                const contentType = fileData.content_type || '';
                
                // 解码Base64内容
                const binaryContent = atob(fileData.content_base64);
                const bytes = new Uint8Array(binaryContent.length);
                for (let i = 0; i < binaryContent.length; i++) {
                    bytes[i] = binaryContent.charCodeAt(i);
                }
                
                // 根据内容类型处理
                if (contentType.startsWith('image/')) {
                    // 图片内容
                    const blob = new Blob([bytes], { type: contentType });
                    const imageUrl = URL.createObjectURL(blob);
                    
                    const img = document.createElement('img');
                    img.src = imageUrl;
                    img.className = 'image-content';
                    img.alt = fileData.filename;
                    contentDisplayDiv.appendChild(img);
                } 
                else if (contentType === 'application/json') {
                    // JSON内容
                    try {
                        const textDecoder = new TextDecoder('utf-8');
                        const jsonText = textDecoder.decode(bytes);
                        const jsonObj = JSON.parse(jsonText);
                        
                        const pre = document.createElement('pre');
                        pre.className = 'json-content';
                        pre.textContent = JSON.stringify(jsonObj, null, 2);
                        contentDisplayDiv.appendChild(pre);
                    } catch (e) {
                        showError(`无法解析JSON内容: ${e.message}`);
                    }
                }
                else if (contentType.startsWith('text/')) {
                    // 文本内容
                    try {
                        const textDecoder = new TextDecoder('utf-8');
                        const text = textDecoder.decode(bytes);
                        
                        const pre = document.createElement('pre');
                        pre.className = 'text-content';
                        pre.textContent = text;
                        contentDisplayDiv.appendChild(pre);
                    } catch (e) {
                        showError(`无法解码文本内容: ${e.message}`);
                    }
                }
                else {
                    // 其他二进制内容
                    const div = document.createElement('div');
                    div.className = 'binary-content';
                    div.textContent = `[二进制数据 (${bytes.length} 字节), 类型: ${contentType}]`;
                    contentDisplayDiv.appendChild(div);
                }
            } else if (fileData.type === 'error') {
                // 显示错误信息
                showError(fileData.message || '未知错误');
            } else {
                // 没有内容
                const div = document.createElement('div');
                div.className = 'binary-content';
                div.textContent = '没有可显示的内容';
                contentDisplayDiv.appendChild(div);
            }
        }
        
        // 显示错误信息
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            contentDisplayDiv.appendChild(errorDiv);
        }
        
        // WebSocket 事件处理
        socket.onopen = function(event) {
            console.log("WebSocket 连接已建立");
            statusDiv.textContent = '已连接到服务器';
            statusDiv.className = 'status connected';
        };
        
        socket.onmessage = function(event) {
            console.log("从服务器收到消息");
            
            try {
                // 尝试解析JSON消息
                const data = JSON.parse(event.data);
                
                // 根据消息类型处理
                if (data.type === 'file_content') {
                    console.log(`收到文件内容: ${data.filename}`);
                    // 显示文件内容
                    displayFileContent(data);
                    // 添加到历史记录
                    addToHistory(data);
                }
                else if (data.type === 'file_update') {
                    console.log(`收到文件更新通知: ${data.path}`);
                    // 显示文件路径信息
                    fileNameSpan.textContent = data.path.split('/').pop() || data.path.split('\\').pop() || '未知文件';
                    filePathSpan.textContent = data.path;
                    timestampSpan.textContent = data.timestamp ? formatTimestamp(data.timestamp) : '-';
                    contentTypeSpan.textContent = '未知类型';
                    
                    // 显示等待消息
                    contentDisplayDiv.innerHTML = '<div class="binary-content">收到文件路径更新，但没有文件内容。这可能是旧版本的服务器消息格式。</div>';
                }
                else if (data.type === 'connection') {
                    console.log("收到连接状态消息:", data.status);
                    if (data.config) {
                        console.log("服务器配置:", data.config);
                    }
                }
                else if (data.type === 'error') {
                    console.error("收到错误消息:", data.message);
                    showError(data.message);
                }
                else {
                    console.log("收到未知类型的消息:", data);
                }
            } catch (e) {
                console.error("解析消息时出错:", e);
                showError(`无法解析服务器消息: ${e.message}`);
            }
        };
        
        socket.onclose = function(event) {
            console.log("WebSocket 连接已关闭", event);
            statusDiv.textContent = '连接已断开';
            statusDiv.className = 'status disconnected';
        };
        
        socket.onerror = function(error) {
            console.error("WebSocket 发生错误:", error);
            statusDiv.textContent = '连接错误';
            statusDiv.className = 'status disconnected';
        };
        
        // 定期发送ping以保持连接
        setInterval(() => {
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    type: 'ping',
                    timestamp: Math.floor(Date.now() / 1000)
                }));
            }
        }, 30000); // 每30秒发送一次
    </script>
</body>
</html>