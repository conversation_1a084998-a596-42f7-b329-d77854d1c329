# file: services.py

from watchfile import FileWatcher
import os
import threading
from typing import Optional

# --- 创建 FileWatcher 的全局单例实例 ---
# 使用最简单的方法：只在第一次调用时初始化，并且不在模块级别创建实例

# 检测是否为主进程（Flask 应用进程）
# 改进的主进程检测逻辑，考虑更多情况
import sys

# 方法1：检查是否是直接运行的脚本（而非模块导入）
_is_main = __name__ == "__main__"

# 方法2：检查是否是 Flask 应用进程
_is_flask_app = False
if hasattr(sys.modules.get("__main__", None), "__file__"):
    main_file = str(getattr(sys.modules.get("__main__", None), "__file__", ""))
    _is_flask_app = "app.py" in main_file or "fancy_dashboard.py" in main_file

# 方法3：检查是否是 multiprocessing 子进程
_is_multiprocessing_child = "__parents_main__" in sys.modules

# 综合判断：不是子进程，并且是 Flask 应用或主模块
_is_main_process = (not _is_multiprocessing_child) and (_is_flask_app or _is_main)

print(
    f"[DEBUG] services.py 模块被导入，进程ID: {os.getpid()}, 主进程: {_is_main_process}"
)

# 全局变量
_file_watcher_instance = None
_initialization_lock = threading.Lock()
_initialized = False


def get_file_watcher():
    """获取 FileWatcher 单例实例"""
    global _file_watcher_instance, _initialized

    # 如果不是主进程，返回一个虚拟实例或 None
    if not _is_main_process:
        print(f"[进程 {os.getpid()}] 检测到子进程，跳过 FileWatcher 初始化")
        # 返回一个虚拟的 FileWatcher 实例，但不启动服务
        if _file_watcher_instance is None:

            class DummyFileWatcher:

                def __init__(self):
                    self._thread: Optional[threading.Thread] = None  # type: ignore

                def start(self):
                    pass

                def stop(self):
                    pass

                def print_config(self):
                    pass

            _file_watcher_instance = DummyFileWatcher()
        return _file_watcher_instance

    # 如果已经初始化，直接返回
    if _initialized and _file_watcher_instance is not None:
        return _file_watcher_instance

    # 使用锁确保线程安全的初始化
    with _initialization_lock:
        # 双重检查锁定
        if _initialized and _file_watcher_instance is not None:
            return _file_watcher_instance

        print(f"[主进程 {os.getpid()}] 正在初始化 FileWatcher 服务...")

        # 在创建实例时，就可以从环境变量加载初始配置
        # 也可以在这里设置一个默认的"安全"配置
        os.environ.setdefault("WATCH_FOLDER", "logs_default")
        os.environ.setdefault("FILE_PATTERN", "*.log")
        os.environ.setdefault("WEBSOCKET_HOST", "localhost")
        os.environ.setdefault("WEBSOCKET_PORT", "8765")

        _file_watcher_instance = FileWatcher()
        _initialized = True

        print(f"[主进程 {os.getpid()}] FileWatcher 服务已成功初始化")

        return _file_watcher_instance


# 直接创建实例，不使用代理
file_watcher = get_file_watcher()

# 确保在主进程中自动启动FileWatcher服务
if _is_main_process:
    try:
        print(f"[主进程 {os.getpid()}] 正在启动 FileWatcher 服务...")
        file_watcher.start()
        print(f"[主进程 {os.getpid()}] FileWatcher 服务已成功启动")
    except Exception as e:
        print(f"[错误] 启动 FileWatcher 服务失败: {str(e)}")
        # 不抛出异常，以免影响主应用
