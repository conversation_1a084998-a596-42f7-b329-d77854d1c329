2025/08/01 18:30:42 [emerg] 5820#29880: unknown "prefix" variable
2025/08/01 18:31:52 [emerg] 30324#4532: invalid variable name in C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/conf/nginx.conf:51
2025/08/01 18:33:00 [error] 3192#28520: *1 directory index of "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/../terrain_data/sysu_sz/" is forbidden, client: 127.0.0.1, server: localhost, request: "GET /terrain_data/sysu_sz/ HTTP/1.1", host: "localhost:8000"
2025/08/01 18:33:00 [error] 3192#28520: *1 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost:8000", referrer: "http://localhost:8000/terrain_data/sysu_sz/"
2025/08/01 18:33:01 [error] 3192#28520: *1 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:34:06 [error] 3192#28520: *2 directory index of "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/../terrain_data/" is forbidden, client: 127.0.0.1, server: localhost, request: "GET /terrain_data/ HTTP/1.1", host: "localhost:8000"
2025/08/01 18:34:07 [error] 3192#28520: *2 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:34:12 [error] 3192#28520: *2 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:34:19 [error] 3192#28520: *2 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:14 [error] 3192#28520: *3 directory index of "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/../terrain_data/sysu_sz/" is forbidden, client: 127.0.0.1, server: localhost, request: "GET /terrain_data/sysu_sz/ HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:15 [error] 3192#28520: *3 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:46 [error] 3192#28520: *3 directory index of "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/../terrain_data/sysu_sz/" is forbidden, client: 127.0.0.1, server: localhost, request: "GET /terrain_data/sysu_sz/ HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:47 [error] 3192#28520: *3 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:47 [error] 3192#28520: *3 directory index of "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/../terrain_data/sysu_sz/" is forbidden, client: 127.0.0.1, server: localhost, request: "GET /terrain_data/sysu_sz/ HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:48 [error] 3192#28520: *3 directory index of "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/../terrain_data/sysu_sz/" is forbidden, client: 127.0.0.1, server: localhost, request: "GET /terrain_data/sysu_sz/ HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:48 [error] 3192#28520: *3 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:39:50 [error] 3192#28520: *3 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/01 18:40:33 [emerg] 1792#12116: no "events" section in configuration
2025/08/01 18:40:36 [emerg] 24768#29704: no "events" section in configuration
2025/08/01 18:41:33 [error] 27612#15368: *1 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/02 00:14:24 [error] 16476#35000: *175 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/02 00:14:25 [error] 16476#35000: *175 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/02 00:14:26 [error] 16476#35000: *175 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/02 00:14:28 [error] 16476#35000: *176 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /favicon.ico HTTP/1.1", host: "localhost:8000", referrer: "http://localhost:8000/terrain_data/sysu_sz/layer.json"
2025/08/02 00:14:29 [error] 16476#35000: *175 CreateFile() "C:\Users\<USER>\Documents\Arcweb\nginx-1.28.0/html/service-worker.js" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: localhost, request: "GET /service-worker.js HTTP/1.1", host: "localhost:8000"
2025/08/06 17:23:35 [error] 368#6492: *17 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 17:41:08 [error] 368#6492: *25 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 17:41:11 [error] 368#6492: *27 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 18:29:14 [error] 368#6492: *107 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 18:29:17 [error] 368#6492: *109 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 18:29:20 [error] 368#6492: *111 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 19:47:31 [error] 368#6492: *132 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 19:47:33 [error] 368#6492: *134 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 19:50:13 [error] 368#6492: *143 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 19:50:52 [error] 368#6492: *145 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 21:55:11 [error] 9016#12492: *75 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 22:11:26 [error] 9016#12492: *155 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 22:20:39 [error] 9016#12492: *157 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 22:22:33 [error] 9016#12492: *159 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 22:23:07 [error] 9016#12492: *168 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 22:23:10 [error] 9016#12492: *170 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 22:23:14 [error] 9016#12492: *172 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 22:23:17 [error] 9016#12492: *174 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:14:15 [error] 9016#12492: *176 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:14:18 [error] 9016#12492: *178 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:14:34 [error] 9016#12492: *180 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:14:37 [error] 9016#12492: *182 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:33:16 [error] 9016#12492: *184 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:33:18 [error] 9016#12492: *186 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:34:58 [error] 9016#12492: *190 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:35:38 [error] 9016#12492: *194 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:36:45 [error] 9016#12492: *200 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:36:48 [error] 9016#12492: *202 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:42:55 [error] 9016#12492: *204 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:42:57 [error] 9016#12492: *206 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:49:00 [error] 9016#12492: *212 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:49:00 [error] 9016#12492: *214 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:52:59 [error] 9016#12492: *216 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:52:59 [error] 9016#12492: *218 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:54:36 [error] 9016#12492: *224 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:54:36 [error] 9016#12492: *226 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:57:36 [error] 9016#12492: *237 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/06 23:59:03 [error] 9016#12492: *239 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 00:01:57 [error] 9016#12492: *243 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 00:01:57 [error] 9016#12492: *241 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 00:07:06 [error] 9016#12492: *264 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 00:07:06 [error] 9016#12492: *262 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 00:10:06 [error] 9016#12492: *279 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 00:10:06 [error] 9016#12492: *277 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 00:14:22 [error] 9016#12492: *281 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 00:14:22 [error] 9016#12492: *283 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 00:16:00 [error] 9016#12492: *295 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 00:18:24 [error] 9016#12492: *313 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 00:18:57 [error] 9016#12492: *317 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 00:19:51 [error] 9016#12492: *319 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 08:30:33 [error] 3476#12804: *3 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 08:35:45 [error] 3476#12804: *15 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 08:41:13 [error] 3476#12804: *19 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 08:43:56 [error] 3476#12804: *32 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 09:24:33 [error] 3476#12804: *61 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 09:24:36 [error] 3476#12804: *63 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 09:25:32 [error] 3476#12804: *73 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 09:26:58 [error] 3476#12804: *77 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 09:33:53 [error] 3476#12804: *79 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 09:34:16 [error] 3476#12804: *88 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 09:34:50 [error] 3476#12804: *90 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 10:07:54 [error] 3476#12804: *160 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 10:09:02 [error] 3476#12804: *171 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 10:21:51 [error] 3476#12804: *232 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 10:23:53 [error] 3476#12804: *239 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:13:12 [error] 3476#12804: *255 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:14:35 [error] 3476#12804: *270 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:15:23 [error] 3476#12804: *274 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:15:47 [error] 3476#12804: *277 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:15:50 [error] 3476#12804: *279 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:16:22 [error] 3476#12804: *292 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:16:39 [error] 3476#12804: *295 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:17:31 [error] 3476#12804: *297 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:17:50 [error] 3476#12804: *300 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:17:52 [error] 3476#12804: *302 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:17:58 [error] 3476#12804: *304 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:18:01 [error] 3476#12804: *306 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:18:38 [error] 3476#12804: *309 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:18:44 [error] 3476#12804: *312 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:18:49 [error] 3476#12804: *314 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:19:03 [error] 3476#12804: *316 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:20:24 [error] 3476#12804: *321 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:20:26 [error] 3476#12804: *323 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:20:29 [error] 3476#12804: *325 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:42:09 [error] 3476#12804: *332 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:42:09 [error] 3476#12804: *334 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 12:48:21 [error] 3476#12804: *351 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 15:49:55 [error] 3476#12804: *382 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 16:52:19 [error] 3476#12804: *515 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 21:22:24 [error] 3476#12804: *568 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 21:24:37 [error] 3476#12804: *574 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 21:25:52 [error] 3476#12804: *579 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 21:27:21 [error] 3476#12804: *587 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 21:32:14 [error] 3476#12804: *599 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 21:33:44 [error] 3476#12804: *607 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 21:46:25 [error] 3476#12804: *613 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 21:47:14 [error] 3476#12804: *617 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 21:52:46 [error] 3476#12804: *629 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 21:58:07 [error] 3476#12804: *644 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 22:52:39 [error] 3476#12804: *716 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 23:07:45 [error] 3476#12804: *742 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 23:08:29 [error] 3476#12804: *744 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/07 23:12:02 [error] 3476#12804: *750 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 11:07:46 [error] 5788#3788: *18 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 11:16:43 [error] 5788#3788: *28 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:37:57 [error] 5788#3788: *185 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:45:41 [error] 5788#3788: *214 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:46:08 [error] 5788#3788: *220 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:46:11 [error] 5788#3788: *222 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:49:06 [error] 5788#3788: *229 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:49:24 [error] 5788#3788: *233 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:49:30 [error] 5788#3788: *235 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:49:44 [error] 5788#3788: *237 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:50:10 [error] 5788#3788: *239 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:53:27 [error] 5788#3788: *244 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:55:59 [error] 5788#3788: *248 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:56:08 [error] 5788#3788: *250 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:56:52 [error] 5788#3788: *252 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:58:10 [error] 5788#3788: *257 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 13:59:29 [error] 5788#3788: *261 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:02:43 [error] 5788#3788: *264 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:04:07 [error] 5788#3788: *266 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:04:13 [error] 5788#3788: *268 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:10:22 [error] 5788#3788: *271 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:19:18 [error] 5788#3788: *274 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:26:46 [error] 5788#3788: *277 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:28:42 [error] 5788#3788: *281 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:39:11 [error] 5788#3788: *287 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:39:16 [error] 5788#3788: *289 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:39:30 [error] 5788#3788: *291 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:39:33 [error] 5788#3788: *293 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:40:14 [error] 5788#3788: *295 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:40:26 [error] 5788#3788: *297 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:40:46 [error] 5788#3788: *299 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:40:47 [error] 5788#3788: *301 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:58:05 [error] 5788#3788: *324 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 16:59:21 [error] 5788#3788: *336 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:03:00 [error] 5788#3788: *338 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:04:56 [error] 5788#3788: *342 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:07:16 [error] 5788#3788: *344 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:17:41 [error] 5788#3788: *347 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:17:41 [error] 5788#3788: *349 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:19:30 [error] 5788#3788: *356 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:23:55 [error] 5788#3788: *363 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:22 [error] 5788#3788: *366 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:27 [error] 5788#3788: *368 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:27 [error] 5788#3788: *370 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:27 [error] 5788#3788: *372 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:29 [error] 5788#3788: *375 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:29 [error] 5788#3788: *379 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:29 [error] 5788#3788: *377 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:34 [error] 5788#3788: *382 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:34 [error] 5788#3788: *386 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:34 [error] 5788#3788: *384 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:42 [error] 5788#3788: *389 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:42 [error] 5788#3788: *391 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:59 [error] 5788#3788: *394 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:59 [error] 5788#3788: *396 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:25:59 [error] 5788#3788: *398 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:26:30 [error] 5788#3788: *403 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/08 17:26:30 [error] 5788#3788: *405 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 00:14:55 [error] 5788#3788: *411 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 00:15:32 [error] 5788#3788: *414 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 00:15:36 [error] 5788#3788: *416 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:47:10 [error] 768#6736: *11 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:47:25 [error] 768#6736: *13 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:47:34 [error] 768#6736: *15 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:49:29 [error] 768#6736: *18 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:49:29 [error] 768#6736: *20 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:51:00 [error] 768#6736: *23 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:51:00 [error] 768#6736: *25 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:52:48 [error] 768#6736: *28 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:54:23 [error] 768#6736: *31 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:56:10 [error] 768#6736: *34 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:56:18 [error] 768#6736: *37 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:58:08 [error] 768#6736: *39 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:58:53 [error] 768#6736: *44 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 09:59:34 [error] 768#6736: *51 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:01:06 [error] 768#6736: *53 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:01:10 [error] 768#6736: *55 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:02:30 [error] 768#6736: *65 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:02:33 [error] 768#6736: *67 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:02:33 [error] 768#6736: *69 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:04:42 [error] 768#6736: *80 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:09:23 [error] 768#6736: *85 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:09:51 [error] 768#6736: *89 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:10:04 [error] 768#6736: *93 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:11:48 [error] 768#6736: *96 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:16:56 [error] 768#6736: *101 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:16:59 [error] 768#6736: *103 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:17:05 [error] 768#6736: *105 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 10:17:10 [error] 768#6736: *107 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:22:58 [error] 21672#14220: *67 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:24:31 [error] 21672#14220: *70 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:26:10 [error] 21672#14220: *73 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:26:20 [error] 21672#14220: *75 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:26:21 [error] 21672#14220: *77 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:26:22 [error] 21672#14220: *79 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:26:22 [error] 21672#14220: *81 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:27:10 [error] 21672#14220: *85 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:27:49 [error] 21672#14220: *90 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:28:34 [error] 21672#14220: *95 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:28:34 [error] 21672#14220: *93 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:28:35 [error] 21672#14220: *97 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:28:35 [error] 21672#14220: *99 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:28:38 [error] 21672#14220: *101 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:29:29 [error] 21672#14220: *104 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:30:13 [error] 21672#14220: *107 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:30:26 [error] 21672#14220: *111 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:30:46 [error] 21672#14220: *115 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:32:35 [error] 21672#14220: *118 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:32:37 [error] 21672#14220: *120 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:33:35 [error] 21672#14220: *123 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:33:35 [error] 21672#14220: *125 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:33:35 [error] 21672#14220: *127 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:33:47 [error] 21672#14220: *131 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:34:03 [error] 21672#14220: *135 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:34:38 [error] 21672#14220: *138 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:34:38 [error] 21672#14220: *140 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:36:04 [error] 21672#14220: *143 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:36:07 [error] 21672#14220: *147 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:36:07 [error] 21672#14220: *145 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:36:09 [error] 21672#14220: *149 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:36:09 [error] 21672#14220: *151 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:37:34 [error] 21672#14220: *154 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:37:56 [error] 21672#14220: *157 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:38:11 [error] 21672#14220: *160 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:38:15 [error] 21672#14220: *162 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:38:15 [error] 21672#14220: *164 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:38:16 [error] 21672#14220: *166 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:38:16 [error] 21672#14220: *168 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:38:19 [error] 21672#14220: *171 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:38:43 [error] 21672#14220: *175 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:39:52 [error] 21672#14220: *177 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:40:02 [error] 21672#14220: *179 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:40:14 [error] 21672#14220: *183 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:41:15 [error] 21672#14220: *186 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:41:29 [error] 21672#14220: *190 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:41:42 [error] 21672#14220: *194 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:42:14 [error] 21672#14220: *197 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:42:18 [error] 21672#14220: *199 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:42:46 [error] 21672#14220: *203 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:43:05 [error] 21672#14220: *209 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:58:07 [error] 21672#14220: *216 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:58:18 [error] 21672#14220: *219 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:58:18 [error] 21672#14220: *221 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 16:58:18 [error] 21672#14220: *223 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 17:02:47 [error] 21672#14220: *226 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 17:49:02 [error] 21672#14220: *230 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 17:49:43 [error] 21672#14220: *235 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 17:50:02 [error] 21672#14220: *238 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 17:50:16 [error] 21672#14220: *241 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 17:50:42 [error] 21672#14220: *244 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 17:50:49 [error] 21672#14220: *247 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 17:51:23 [error] 21672#14220: *254 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/09 17:51:38 [error] 21672#14220: *258 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/10 18:11:00 [error] 22320#22376: *15 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 15:24:16 [error] 22656#9152: *193 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 15:24:27 [error] 22656#9152: *196 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 15:24:27 [error] 22656#9152: *198 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 15:24:28 [error] 22656#9152: *200 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 15:24:28 [error] 22656#9152: *202 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:40:13 [error] 22656#9152: *309 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:40:14 [error] 22656#9152: *311 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:40:16 [error] 22656#9152: *313 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:40:16 [error] 22656#9152: *315 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:40:22 [error] 22656#9152: *318 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:40:22 [error] 22656#9152: *320 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:40:34 [error] 22656#9152: *323 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:40:34 [error] 22656#9152: *325 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:00 [error] 22656#9152: *328 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:00 [error] 22656#9152: *330 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:01 [error] 22656#9152: *332 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:06 [error] 22656#9152: *336 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:15 [error] 22656#9152: *341 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:15 [error] 22656#9152: *339 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:16 [error] 22656#9152: *343 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:25 [error] 22656#9152: *346 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:36 [error] 22656#9152: *349 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:45 [error] 22656#9152: *352 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:45 [error] 22656#9152: *354 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:46 [error] 22656#9152: *356 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:47 [error] 22656#9152: *358 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:41:57 [error] 22656#9152: *361 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:42:18 [error] 22656#9152: *365 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:42:59 [error] 22656#9152: *370 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:42:59 [error] 22656#9152: *368 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:42:59 [error] 22656#9152: *372 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:43:28 [error] 22656#9152: *376 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:44:14 [error] 22656#9152: *381 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:44:14 [error] 22656#9152: *383 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:44:14 [error] 22656#9152: *385 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:44:15 [error] 22656#9152: *387 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:45:17 [error] 22656#9152: *391 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:47:23 [error] 22656#9152: *394 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:47:23 [error] 22656#9152: *396 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:47:38 [error] 22656#9152: *399 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:47:38 [error] 22656#9152: *401 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:47:40 [error] 22656#9152: *405 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:47:40 [error] 22656#9152: *403 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/11 23:47:41 [error] 22656#9152: *407 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:03:06 [error] 22656#9152: *420 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:03:07 [error] 22656#9152: *422 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:03:21 [error] 22656#9152: *425 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:03:36 [error] 22656#9152: *428 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:03:36 [error] 22656#9152: *430 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:03:47 [error] 22656#9152: *433 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:03:47 [error] 22656#9152: *435 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:03:47 [error] 22656#9152: *437 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:03:47 [error] 22656#9152: *439 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:03:51 [error] 22656#9152: *441 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:04:03 [error] 22656#9152: *445 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:04:23 [error] 22656#9152: *449 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:04:37 [error] 22656#9152: *452 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:04:37 [error] 22656#9152: *454 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:04:41 [error] 22656#9152: *456 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
2025/08/12 00:04:41 [error] 22656#9152: *458 connect() failed (10061: No connection could be made because the target machine actively refused it) while connecting to upstream, client: 127.0.0.1, server: localhost, request: "GET /ws/ HTTP/1.1", upstream: "http://127.0.0.1:8765/ws/", host: "localhost:8000"
