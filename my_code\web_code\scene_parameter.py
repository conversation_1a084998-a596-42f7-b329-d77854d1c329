"""
场景参数模块
提供雷达场景参数的配置、管理和应用功能
"""

from flask import Blueprint, jsonify
from flask_jwt_extended import jwt_required
import my_code.radar_code as radar_code
from config.database_config import (
    get_client,
    ApiResponse,
)
from typing import Dict
from validator import *
from config.logging_config import get_api_logger

# 配置API模块日志器
logger = get_api_logger()

# 创建蓝图
scene_parameter = Blueprint("scene_parameter", __name__)


# 查询成像参数
@scene_parameter.route("/check_scene_parameters", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="查询场景参数")
@handle_database_exceptions
@validate_request("radar_ID")
def web_check_scene_parameter(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    scene_parameter_data: Dict[str, int | float] = {}
    if radar_code.manager.is_radar_registered(radar_id):
        logger.info(f"[雷达{radar_id}] 雷达已注册或在缓存中，开始查询状态")
        radar = radar_code.manager.get_radar(radar_id)
        if radar:
            result = radar.query_scene_parameter()
            radar_scene_parameter_collection = get_collection(
                get_client(), radar_id, "scene_parameter"
            )

            wrap = radar_scene_parameter_collection.find(
                {"serial_number": {"$gte": 1, "$lte": 24}}
            ).not_empty()
            code_name = wrap.field("code_name").not_empty().unwrap()
            data = wrap.field("data").not_empty().unwrap()

            scene_parameter_data = {}
            for each_code_name, each_data in zip(code_name, data):
                scene_parameter_data[each_code_name] = each_data
        else:
            # 这种情况不应该发生，因为is_radar_registered已经检查了缓存
            # 但为了健壮性，我们还是添加这个检查
            logger.warning(f"[雷达{radar_id}] 雷达在注册表中但无法获取实例")
            result = "雷达离线"
    else:
        logger.warning(f"[雷达{radar_id}] 雷达未注册或离线")
        result = "雷达离线"

    if result == "success":
        return (
            jsonify(
                {
                    "status": "success",  # 状态信息
                    "message": "场景参数查询成功",  # 附加消息
                    "data": scene_parameter_data,  # 数据
                }
            ),
            200,
        )
    elif result == "error":
        return (
            jsonify(
                {
                    "status": "warning",
                    "message": "查询失败，数据为雷达最后在线时的算法参数",
                    "data": scene_parameter_data,
                }
            ),
            500,
        )
    else:
        return (
            jsonify(
                {"status": "error", "message": result, "data": scene_parameter_data}
            ),
            500,
        )


# 更新成像参数
@scene_parameter.route("/update_scene_parameter", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="更新场景参数")
@handle_database_exceptions
@validate_request("radar_ID")
def web_update_scene_parameter(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    # 去掉 kwargs["data"] 里面的 "radar_ID"
    del kwargs["data"]["radar_ID"]
    scene_parameter = kwargs["data"]
    logger.info(f"开始更新场景参数 - 请求雷达ID: {radar_id}")
    logger.info(f"请求参数: {scene_parameter}")

    result = radar_code.manager.get_radar(radar_id).set_scene_parameter(scene_parameter)
    if result == "success":
        return jsonify({"status": "success", "message": "雷达成像参数设置成功"}), 200
    elif result == "error":
        return jsonify({"status": "error", "message": "雷达成像参数设置失败"}), 500
    else:
        return jsonify({"status": "warning", "message": result}), 500
