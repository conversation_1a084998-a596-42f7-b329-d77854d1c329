# WebSocket 连接问题修复日志
# 日期: 2025/8/7

## 问题描述
当使用 uv run app.py 启动应用时，WebSocket 连接正常工作。
但当使用 dashboard (fancy_dashboard.py) 启动时，前端 WebSocket 连接会关闭，错误代码为 1001。

## 原因分析
问题出在 services.py 文件中的主进程检测逻辑。当通过 fancy_dashboard.py 启动时，
由于使用了 multiprocessing.Process 创建子进程运行 Flask 应用，导致 WebSocket 服务
没有正确初始化。

## 修改前的代码
```python
# 检测是否为主进程（Flask 应用进程）
# 通过检查是否有 Flask 应用的特征来判断
import sys

_is_main_process = hasattr(
    sys.modules.get("__main__", None), "__file__"
) and "app.py" in str(getattr(sys.modules.get("__main__", None), "__file__", ""))

print(
    f"[DEBUG] services.py 模块被导入，进程ID: {os.getpid()}, 主进程: {_is_main_process}"
)
```

## 修改后的代码
```python
# 检测是否为主进程（Flask 应用进程）
# 改进的主进程检测逻辑，考虑更多情况
import sys

# 方法1：检查是否是直接运行的脚本（而非模块导入）
_is_main = __name__ == "__main__"

# 方法2：检查是否是 Flask 应用进程
_is_flask_app = False
if hasattr(sys.modules.get("__main__", None), "__file__"):
    main_file = str(getattr(sys.modules.get("__main__", None), "__file__", ""))
    _is_flask_app = "app.py" in main_file or "fancy_dashboard.py" in main_file

# 方法3：检查是否是 multiprocessing 子进程
_is_multiprocessing_child = "__parents_main__" in sys.modules

# 综合判断：不是子进程，并且是 Flask 应用或主模块
_is_main_process = (not _is_multiprocessing_child) and (_is_flask_app or _is_main)

print(
    f"[DEBUG] services.py 模块被导入，进程ID: {os.getpid()}, 主进程: {_is_main_process}"
)
```

## 代码解释

### 修改前的代码
原代码使用了一个简单的检测方法来判断当前进程是否为主进程：
1. 检查 __main__ 模块是否有 __file__ 属性
2. 检查该属性是否包含字符串 "app.py"

这种方法有明显的局限性：
- 只有直接运行 app.py 时才会返回 True
- 通过其他脚本（如 fancy_dashboard.py）启动时会返回 False
- 无法正确处理多进程情况

### 修改后的代码
新代码采用了更全面的检测策略：

1. **方法1**：检查是否是直接运行的脚本
   - 通过 `__name__ == "__main__"` 判断当前模块是否是主模块

2. **方法2**：检查是否是 Flask 应用进程
   - 不仅检查 "app.py"，还检查 "fancy_dashboard.py"
   - 这样无论通过哪种方式启动，都能正确识别

3. **方法3**：检查是否是 multiprocessing 子进程
   - 通过检查 `__parents_main__` 是否在 sys.modules 中
   - 这是 multiprocessing 模块创建子进程时的特征

4. **综合判断**：
   - 不是子进程，并且是 Flask 应用或主模块
   - 这样可以确保在各种启动方式下都能正确初始化 WebSocket 服务

## 修复效果
通过这个修改，无论是通过 `uv run app.py` 还是通过 `fancy_dashboard.py` 启动应用，
WebSocket 服务都能正确初始化并保持连接，解决了前端连接关闭的问题。