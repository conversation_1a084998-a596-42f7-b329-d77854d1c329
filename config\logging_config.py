"""
日志配置
使用范围：所有项目代码
"""

# 统一的日志配置模块
import logging
import logging.handlers
import os
from datetime import datetime
import collections
import threading
from typing import Deque


class LogBufferHandler(logging.Handler):
    """(选用，如果想让日志记录存储在内存缓冲区中)
    一个自定义的日志处理器，将日志记录存储在内存缓冲区中。
    """

    def __init__(self, capacity: int = 1000):
        """
        初始化 Handler

        :param capacity: 缓冲区最大容量，防止内存溢出。
        """
        # 1. 必须首先调用父类的 __init__
        super().__init__()
        self.capacity = capacity
        self.lock
        # 2. 初始化缓冲区
        # 类型提示：一个最大长度为 capacity 的 deque，存储 LogRecord 对象
        # 注意：collections.deque 在类型提示中是泛型的 (3.9+)
        # 如果兼容性有问题，可以简化为 : collections.deque
        # 或者使用 List[logging.LogRecord] (但这不是循环缓冲区的精确表示)
        self.buffer: Deque[logging.LogRecord] = collections.deque(maxlen=capacity)

        # 3. 调用 createLock() 创建 self.lock
        # 父类 Handler.__init__ 或 createLock 会设置 self.lock: threading.Lock | None
        # 我们依赖父类的行为，不在此处手动创建或添加类型注解
        # self.createLock() # 通常在 Handler.__init__ 内部调用，无需再次调用

        # --- 验证 lock 是否已创建 (调试用) ---
        # if self.lock is None:
        #     raise RuntimeError("Handler's lock was not created by createLock()")
        # print(f"LogBufferHandler initialized. Lock type: {type(self.lock)}, Is RLock: {isinstance(self.lock, threading.RLock)}")

    def emit(self, record):
        """
        当有日志记录需要处理时，此方法会被调用。

        :param record: logging.LogRecord 对象
        """
        # 防止在格式化日志时出现错误导致 Handler 崩溃
        # 防止在处理日志时出现错误导致 Handler 崩溃
        try:
            # 确保 self.lock 存在 (尽管 Handler 规范要求它存在)
            # Handler 基类应该保证 self.lock 在 createLock() 后不为 None
            if self.lock is not None:  # Handler 规范要求 lock 存在
                # 获取锁
                self.acquire()
                try:
                    # --- 临界区开始 ---
                    # 将 LogRecord 对象本身存入缓冲区
                    self.buffer.append(record)
                    # --- 临界区结束 ---
                finally:
                    # 确保锁被释放
                    self.release()
            else:
                # 如果 lock 为 None (理论上不应该发生)，则不使用锁
                # 这在多线程环境下是不安全的，但可以避免崩溃
                self.buffer.append(record)
        except Exception as e:
            # 打印具体错误，方便调试 (在实际应用中可能需要更谨慎的日志记录)
            # import sys
            # print(f"Error in LogBufferHandler.emit: {e}", file=sys.stderr)
            # 即使 Handler 出错，也不要影响主程序
            self.handleError(record)

    def get_records(self):
        """
        获取缓冲区中所有当前的日志记录。

        :return: LogRecord 对象列表的副本。
        """
        """
        获取缓冲区中所有当前的日志记录。
        :return: LogRecord 对象列表的副本。
        """
        # 同样检查 lock
        if self.lock is not None:
            self.acquire()
            try:
                # 返回一个副本，避免在 Live 更新时缓冲区被修改
                # list(self.buffer) 是线程安全的，因为它创建了一个新列表
                # 但访问 self.buffer 本身（一个可变对象）在没有锁的情况下不是原子的
                # 因此，最好在锁内进行
                return list(self.buffer)
            finally:
                self.release()
        else:
            return list(self.buffer)  # 不安全的副本

    def clear_records(self):
        """
        清空缓冲区。
        """
        # 同样检查 lock
        if self.lock is not None:
            self.acquire()
            try:
                self.buffer.clear()
            finally:
                self.release()
        else:
            self.buffer.clear()  # 不安全的操作


# --- 自定义 Buffer 处理器 ---
log_buffer_handler = LogBufferHandler(capacity=1000)  # 可以调整容量


def setup_logging():
    """统一的日志配置"""
    # 创建日志目录
    if not os.path.exists("../log"):
        os.makedirs("../log")

    # 清除现有的处理器，避免重复配置
    root_logger = logging.getLogger()
    if root_logger.hasHandlers():
        root_logger.handlers.clear()
    root_logger.setLevel(logging.INFO)  # 设置根记录器级别

    # 文件处理器
    file_handler = logging.handlers.RotatingFileHandler(
        "../log/radar.log",
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding="utf-8",
    )
    file_handler.setLevel(logging.INFO)  # 文件记录 INFO 及以上

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(
        logging.INFO
    )  # 控制台记录 INFO 及以上级别的日志，确保在终端显示

    # 设置级别，确保捕获需要在 rich 界面显示的日志
    log_buffer_handler.setLevel(logging.INFO)

    # 格式化器
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    detailed_formatter = logging.Formatter(  # 可以为 Buffer Handler 使用更详细的格式（如果需要）
        "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
    )

    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    # Buffer Handler 可以使用相同或不同的格式化器
    log_buffer_handler.setFormatter(formatter)

    # root_logger.addHandler(file_handler)
    # root_logger.addHandler(console_handler)
    # root_logger.addHandler(log_buffer_handler)  # 添加自定义 Handler

    # 防止日志传播到上级logger造成重复输出
    root_logger.propagate = False


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志器"""
    return logging.getLogger(name)


# 预定义的功能模块日志器
def get_api_logger() -> logging.Logger:
    """获取API模块日志器"""
    return get_logger("arcweb.api")


def get_radar_logger() -> logging.Logger:
    """获取雷达核心模块日志器"""
    return get_logger("arcweb.radar")


def get_server_logger() -> logging.Logger:
    """获取雷达服务器模块日志器"""
    return get_logger("arcweb.server")


def get_simulator_logger() -> logging.Logger:
    """获取模拟器模块日志器"""
    return get_logger("arcweb.simulator")


def get_worker_logger() -> logging.Logger:
    """获取Worker模块日志器"""
    return get_logger("arcweb.worker")


# --- 获取 Buffer Handler 的函数 ---
def get_log_buffer_handler():
    """获取全局的 LogBufferHandler 实例"""
    return log_buffer_handler


def configure_module_loggers():
    """配置各模块日志器的特定设置"""
    # 可以为不同模块设置不同的日志级别
    get_api_logger().setLevel(logging.INFO)
    get_radar_logger().setLevel(logging.INFO)  # 雷达模块需要更详细的日志
    get_server_logger().setLevel(logging.INFO)
    get_simulator_logger().setLevel(logging.INFO)
    get_worker_logger().setLevel(logging.INFO)
