import os
import time
import asyncio
import threading
import fnmatch
import json
import base64
from typing import Set, List, Dict, Union, Any, Optional
from config.logging_config import get_api_logger

logger = get_api_logger()
# --- 依赖 ---
# 请确保已安装: pip install watchdog websockets
import websockets
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# --- 工具类定义 ---


class FileChangeHandler(FileSystemEventHandler):
    """
    一个 Watchdog 事件处理器，当文件发生变化时，它会
    读取文件内容并在一个 asyncio 事件循环中安全地调用一个异步回调函数。
    """

    def __init__(
        self, loop: asyncio.AbstractEventLoop, broadcast_callback, file_pattern: str
    ):
        self.loop = loop
        self.broadcast_callback = broadcast_callback
        self.file_pattern = file_pattern
        self.DEBOUNCE_DELAY = 0.3  # 防抖延迟，单位秒
        self.debounce_timers: Dict[str, threading.Timer] = {}  # 用于存储防抖定时器

    def process_file(self, file_path):
        """处理文件内容并发送"""
        try:
            logger.info(f"[API] 读取文件内容: {file_path}")
            # 等待文件写入完成
            time.sleep(0.1)

            # 读取文件内容
            with open(file_path, "rb") as f:
                content = f.read()

            # 获取文件名和扩展名
            filename = os.path.basename(file_path)
            file_ext = os.path.splitext(filename)[1].lower()

            # 构建数据包
            data_package = {
                "type": "file_content",
                "filename": filename,
                "path": file_path,
                "content_type": self._get_content_type(file_ext),
                "timestamp": time.time(),
                "content": content,  # 二进制内容将在broadcast方法中进行base64编码
            }

            # 发送数据包
            asyncio.run_coroutine_threadsafe(
                self.broadcast_callback(data_package), self.loop
            )
            logger.info(f"[API] 已将文件 '{filename}' 的内容推送给客户端")
        except FileNotFoundError:
            logger.warning(f"[API] 处理时文件已不存在: {file_path}")
        except Exception as e:
            logger.error(f"[API] 读取或发送文件时出错: {str(e)}")

    def _get_content_type(self, extension):
        """根据文件扩展名确定内容类型"""
        # 直接使用内置的映射，不依赖于FileWatcher实例
        content_types = {
            ".png": "image/png",
            ".jpg": "image/jpeg",
            ".jpeg": "image/jpeg",
            ".gif": "image/gif",
            ".bmp": "image/bmp",
            ".txt": "text/plain",
            ".log": "text/plain",
            ".json": "application/json",
            ".xml": "application/xml",
            ".html": "text/html",
            ".csv": "text/csv",
        }
        return content_types.get(extension, "application/octet-stream")

    def debounce_event(self, event):
        """使用防抖机制处理事件"""
        if event.is_directory:
            return

        path = event.src_path

        # 取消之前的定时器（如果存在）
        if path in self.debounce_timers:
            self.debounce_timers[path].cancel()

        # 创建新的定时器
        timer = threading.Timer(self.DEBOUNCE_DELAY, self.process_file, args=[path])
        self.debounce_timers[path] = timer
        timer.start()

    def on_modified(self, event):
        """当文件或目录被修改时调用。"""
        if not event.is_directory and fnmatch.fnmatch(
            os.path.basename(str(event.src_path)), self.file_pattern
        ):
            logger.debug(f"[API] 检测到文件修改: {event.src_path}")
            self.debounce_event(event)

    def on_created(self, event):
        """当文件或目录被创建时调用。"""
        if not event.is_directory and fnmatch.fnmatch(
            os.path.basename(str(event.src_path)), self.file_pattern
        ):
            logger.debug(f"[API] 检测到文件创建: {event.src_path}")
            self.debounce_event(event)


class FileWatcher:
    """
    一个可控的、支持环境变量热更新的文件监控工具。
    它会监控指定文件夹中的文件变化，并通过 WebSocket 进行广播。
    """

    def __init__(self):
        # 从环境变量加载初始配置
        self._load_config()

        # 内部状态
        self._thread: Optional[threading.Thread] = None
        self._observer = None
        self._stop_event = threading.Event()
        self._clients: Set = set()

        logger.info(f"[API] FileWatcher 初始化成功。配置如下:")
        self.print_config()

    def _load_config(self):
        """从环境变量加载配置，并提供合理的默认值。"""
        self.watch_folder = os.environ.get("WATCH_FOLDER", "log")
        self.file_pattern = os.environ.get("FILE_PATTERN", "*.log")
        self.websocket_host = os.environ.get("WEBSOCKET_HOST", "localhost")
        self.websocket_port = int(os.environ.get("WEBSOCKET_PORT", 8765))
        return (
            self.watch_folder,
            self.file_pattern,
            self.websocket_host,
            self.websocket_port,
        )

    def print_config(self):
        """打印当前配置。"""
        logger.info(f"[API] 监控文件夹 (WATCH_FOLDER): {self.watch_folder}")
        logger.info(f"[API] 文件模式 (FILE_PATTERN): {self.file_pattern}")
        logger.info(
            f"[API] WebSocket Host: {self.websocket_host}:{self.websocket_port}"
        )

    async def _websocket_handler(self, websocket):
        """处理新的 WebSocket 连接。"""
        logger.info(f"[API] 客户端 {websocket.remote_address} 已连接。")
        self._clients.add(websocket)
        try:
            # 发送初始连接成功消息
            await websocket.send(
                json.dumps(
                    {
                        "type": "connection",
                        "status": "connected",
                        "config": {
                            "watch_folder": self.watch_folder,
                            "file_pattern": self.file_pattern,
                        },
                    }
                )
            )

            # 保持连接打开，直到客户端断开
            # 使用ping/pong机制保持连接活跃
            while True:
                try:
                    # 等待客户端消息，但设置超时以便定期发送ping
                    message = await asyncio.wait_for(websocket.recv(), timeout=30)

                    # 处理客户端消息
                    try:
                        data = json.loads(message)
                        await self._handle_client_message(websocket, data)
                    except json.JSONDecodeError:
                        logger.warning(f"[API] 收到无效的JSON消息: {message}")
                    except Exception as e:
                        logger.error(f"[API] 处理客户端消息时出错: {str(e)}")

                except asyncio.TimeoutError:
                    # 超时，发送ping以保持连接
                    try:
                        pong_waiter = await websocket.ping()
                        await asyncio.wait_for(pong_waiter, timeout=10)
                        logger.debug("Ping/pong成功")
                    except (
                        asyncio.TimeoutError,
                        websockets.exceptions.ConnectionClosed,
                    ):
                        logger.info(
                            f"Ping超时，客户端 {websocket.remote_address} 可能已断开"
                        )
                        break
                except websockets.exceptions.ConnectionClosed:
                    logger.info(f"[API] 客户端 {websocket.remote_address} 连接已关闭")
                    break
        except Exception as e:
            logger.error(f"[API] WebSocket处理异常: {str(e)}")
        finally:
            logger.info(f"[API] 客户端 {websocket.remote_address} 已断开。")
            if websocket in self._clients:
                self._clients.remove(websocket)

    async def _handle_client_message(self, websocket, data):
        """处理来自客户端的消息"""
        if not isinstance(data, dict):
            logger.warning("收到非字典类型的消息")
            return

        message_type = data.get("type")

        if message_type == "request_file":
            # 客户端请求特定文件的内容
            file_path = data.get("path")
            if file_path and os.path.isfile(file_path):
                try:
                    # 读取文件内容
                    with open(file_path, "rb") as f:
                        content = f.read()

                    # 获取文件名和扩展名
                    filename = os.path.basename(file_path)
                    file_ext = os.path.splitext(filename)[1].lower()

                    # 构建响应
                    content_base64 = base64.b64encode(content).decode("utf-8")
                    response = {
                        "type": "file_content",
                        "request_id": data.get(
                            "request_id"
                        ),  # 返回请求ID以便客户端匹配
                        "filename": filename,
                        "path": file_path,
                        "content_type": self._get_content_type(file_ext),
                        "timestamp": time.time(),
                        "content_base64": content_base64,
                    }

                    # 发送响应
                    await websocket.send(json.dumps(response))
                    logger.info(f"[API] 已发送文件 '{filename}' 的内容给客户端")
                except Exception as e:
                    logger.error(f"[API] 读取或发送文件时出错: {str(e)}")
                    await websocket.send(
                        json.dumps(
                            {
                                "type": "error",
                                "request_id": data.get("request_id"),
                                "message": f"无法读取文件: {str(e)}",
                            }
                        )
                    )
            else:
                await websocket.send(
                    json.dumps(
                        {
                            "type": "error",
                            "request_id": data.get("request_id"),
                            "message": f"文件不存在: {file_path}",
                        }
                    )
                )
        elif message_type == "ping":
            # 客户端发送的ping，回复pong
            await websocket.send(json.dumps({"type": "pong", "timestamp": time.time()}))
        else:
            logger.debug(f"[API] 收到未知类型的消息: {message_type}")

    def _get_content_type(self, extension):
        """根据文件扩展名确定内容类型"""
        # 直接使用内置的映射，不依赖于FileWatcher实例
        content_types = {
            ".png": "image/png",
            ".jpg": "image/jpeg",
            ".jpeg": "image/jpeg",
            ".gif": "image/gif",
            ".bmp": "image/bmp",
            ".txt": "text/plain",
            ".log": "text/plain",
            ".json": "application/json",
            ".xml": "application/xml",
            ".html": "text/html",
            ".csv": "text/csv",
        }
        return content_types.get(extension, "application/octet-stream")

    async def _broadcast(self, data: Union[str, Dict[str, Any]]):
        """向所有连接的客户端广播消息。

        参数:
            data: 可以是字符串路径或包含文件内容的数据包字典
        """
        if not self._clients:
            logger.debug("没有连接的客户端，跳过广播")
            return

        # 创建一个客户端副本，避免在迭代过程中修改集合
        clients_to_remove: List = []

        # 准备要发送的消息
        if isinstance(data, str):
            # 向后兼容：如果是字符串，则视为文件路径
            logger.info(
                f"检测到文件更新，向 {len(self._clients)} 个客户端广播路径: {data}"
            )
            json_message = json.dumps(
                {"type": "file_update", "path": data, "timestamp": time.time()}
            )
        else:
            # 新格式：发送文件内容
            filename = data.get("filename", "unknown")
            logger.info(
                f"检测到文件更新，向 {len(self._clients)} 个客户端广播文件内容: {filename}"
            )

            # 处理二进制内容，转换为base64
            if "content" in data and isinstance(data["content"], bytes):
                # 将二进制内容转换为base64字符串
                content_base64 = base64.b64encode(data["content"]).decode("utf-8")
                # 创建新的数据包，不包含原始二进制内容
                json_data = {k: v for k, v in data.items() if k != "content"}
                json_data["content_base64"] = content_base64
                json_message = json.dumps(json_data)
            else:
                # 如果没有二进制内容，直接序列化
                json_message = json.dumps(data)

        # 为每个客户端单独处理发送，以便隔离错误
        for client in list(self._clients):
            try:
                await client.send(json_message)
            except websockets.exceptions.ConnectionClosed:
                logger.warning(f"[API] 客户端连接已关闭，将从客户端列表中移除")
                clients_to_remove.append(client)
            except Exception as e:
                logger.error(f"[API] 向客户端发送消息时出错: {str(e)}")
                clients_to_remove.append(client)

        # 移除失效的客户端
        for client in clients_to_remove:
            if client in self._clients:
                self._clients.remove(client)

    def _start_observer(self, loop):
        """启动或重启 watchdog 监视器。"""
        # 如果已有监视器，先停止它
        if self._observer and self._observer.is_alive():
            self._observer.stop()
            self._observer.join()

        # 确保监控的文件夹存在
        if not os.path.isdir(self.watch_folder):
            logger.info(
                f"[警告] 文件夹 '{self.watch_folder}' 不存在。正在等待其被创建..."
            )
            # 尝试创建文件夹
            try:
                os.makedirs(self.watch_folder, exist_ok=True)
                logger.info(f"[API] 已创建文件夹: '{self.watch_folder}'")
            except Exception as e:
                logger.error(f"[API] 创建文件夹失败: {str(e)}")

        # 将FileWatcher实例存储在loop中，以便事件处理器访问
        loop._file_watcher = self

        # 创建事件处理器
        event_handler = FileChangeHandler(loop, self._broadcast, self.file_pattern)
        self._observer = Observer()
        # `recursive=True` 意味着也会监控子文件夹
        self._observer.schedule(event_handler, self.watch_folder, recursive=True)
        self._observer.start()
        logger.info(
            f"[API] 已开始在 '{self.watch_folder}' 中监控 '{self.file_pattern}' 文件。"
        )

    def _watch_loop(self):
        """在后台线程中运行的主循环。"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        async def async_main():
            # 启动 WebSocket 服务器，配置心跳检测和超时参数
            server = await websockets.serve(
                self._websocket_handler,
                self.websocket_host,
                self.websocket_port,
                ping_interval=20,  # 每20秒发送一次ping
                ping_timeout=10,  # ping超时时间为10秒
                close_timeout=5,  # 关闭连接的超时时间
            )
            logger.info(
                f"[API] WebSocket 服务器已在 ws://{self.websocket_host}:{self.websocket_port} 上启动。"
            )

            # 启动初始的 watchdog 监视器
            self._start_observer(loop)

            current_config = self._load_config()

            try:
                while not self._stop_event.is_set():
                    # 运行 asyncio 事件循环一小段时间来处理 WebSocket 事件
                    await asyncio.sleep(1.0)

                    # --- 热更新逻辑 ---
                    new_config = self._load_config()
                    # 只关心文件夹和模式的变化
                    if (new_config[0], new_config[1]) != (
                        current_config[0],
                        current_config[1],
                    ):
                        logger.info(f"[API] \n[!] 检测到配置变化，正在热更新...")
                        self.print_config()
                        current_config = new_config
                        # 重启监视器以应用新路径和模式
                        self._start_observer(loop)
            finally:
                logger.info(f"[API] 正在关闭监控...")
                if self._observer and self._observer.is_alive():
                    self._observer.stop()
                    self._observer.join()
                server.close()
                await server.wait_closed()
                logger.info(f"[API] 监控已安全关闭。")

        try:
            loop.run_until_complete(async_main())
        finally:
            loop.close()

    def start(self):
        """启动后台监控线程。"""
        if self._thread is not None and self._thread.is_alive():
            logger.info(f"[API] 监控已经在运行中。")
            return

        self._stop_event.clear()
        self._thread = threading.Thread(target=self._watch_loop, daemon=True)
        self._thread.start()
        logger.info(f"[API] 监控线程已启动。")

    def stop(self):
        """停止后台监控线程。"""
        if self._thread is None or not self._thread.is_alive():
            logger.info(f"[API] 监控未在运行中。")
            return

        self._stop_event.set()
        self._thread.join()  # 等待线程完全退出
        self._thread = None
        logger.info(f"[API] 监控线程已停止。")


# --- 示例用法 ---
if __name__ == "__main__":
    logger.info(f"--- 文件监控工具示例 ---")

    # 设置初始环境变量 (在真实场景中，这通常由启动脚本或操作系统设置)
    os.environ["WATCH_FOLDER"] = "log"
    os.environ["FILE_PATTERN"] = "*.log"
    os.environ["WEBSOCKET_HOST"] = "localhost"
    os.environ["WEBSOCKET_PORT"] = "8765"

    # 1. 创建并启动监控器
    watcher = FileWatcher()
    watcher.start()

    logger.info(f"\n监控已在后台运行。现在您可以：")
    logger.info(
        f"1. 在另一个终端连接 WebSocket 客户端 (例如使用 'websocat ws://localhost:8765')。"
    )
    logger.info(f"2. 修改 'log/radar.log' 文件来观察广播。")
    logger.info(f"3. 在此终端按 Enter 键来模拟环境变量热更新。")
    logger.info(f"4. 按 Ctrl+C 来停止并退出。")

    try:
        # 保持主线程存活，并等待用户输入以触发热更新
        input("\n按 [Enter] 键来模拟热更新 (将监控文件夹改为 'temp_logs')...\n")

        # 模拟配置变化
        logger.info(f"\n--- 正在模拟热更新 ---")
        os.environ["WATCH_FOLDER"] = "temp_logs"
        # 确保新文件夹存在
        os.makedirs("temp_logs", exist_ok=True)

        # 等待一小会儿，让监控线程检测到变化
        time.sleep(1.5)
        logger.info(f"现在监控器应该已经切换到了 'temp_logs' 文件夹。")
        logger.info(f"修改 'temp_logs/test.log' 来观察效果。")

        # 让脚本继续运行
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info(f"\n收到退出信号...")
    finally:
        # 3. 优雅地停止监控器
        logger.info(f"正在停止监控器...")
        watcher.stop()
        logger.info(f"示例程序已退出。")
