"""
管理数据库连接, 专提供给API使用
"""

import logging
from flask import Flask
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
from typing import Dict, Any
from .flask_config import Config

logger = logging.getLogger(__name__)


class MongoConnector:
    _instance = None
    _client: MongoClient[Dict[str, Any]] | None = None
    _initialized_app_hash: int | None = None  # 用于防止对同一个app重复初始化

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MongoConnector, cls).__new__(cls)
        return cls._instance

    def init_app(self, app: Flask):
        # 使用app的hash值来检查是否已为这个特定的app实例初始化过
        if self._initialized_app_hash == hash(app):
            logger.warning(f"MongoConnector已为app '{app.name}' 初始化，跳过。")
            return

        # 关键：只在_client不存在时才创建
        if MongoConnector._client is None:
            # 首先，获取值
            mongo_uri = Config.MONGO_URI_BASE
            if not mongo_uri:
                raise RuntimeError(
                    "数据库配置错误：必须在Flask配置中设置 'MONGO_URI'。"
                )

            logger.info(f"正在创建并初始化MongoDB单例客户端，URI: '{mongo_uri}'")
            try:
                client: MongoClient[Dict[str, Any]] = MongoClient(
                    host=mongo_uri, serverSelectionTimeoutMS=5000
                )
                client.admin.command("ping")
                # **核心改动**：将client赋值给类变量，而不是实例变量
                MongoConnector._client = client
                logger.info("MongoDB单例客户端已成功连接并验证。")
            except (ConnectionFailure, ServerSelectionTimeoutError) as e:
                logger.critical("无法创建MongoDB客户端！", exc_info=True)
                raise ConnectionFailure(f"无法连接到MongoDB: {e}") from e

        # 挂载和注册teardown可以每次都做，因为Flask会处理好重复的teardown函数
        if not hasattr(app, "extensions"):
            app.extensions = {}
        app.extensions["mongo_connector"] = self
        app.teardown_appcontext(self.teardown)
        self._initialized_app_hash = hash(app)

    @property
    def client(self) -> MongoClient[Dict[str, Any]]:
        if self._client is None:
            raise RuntimeError("MongoConnector尚未通过init_app成功初始化。")
        return self._client

    def teardown(self, exception: Any = None):
        pass


# 创建全局的单例连接器实例
mongo_connector = MongoConnector()
